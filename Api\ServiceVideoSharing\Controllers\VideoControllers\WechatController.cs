using BLL.VideoService;
using Common;
using Common.Exceptions;
using Common.Helper;
using Common.JWT;
using Common.Log4Net;
using Common.WX;
using Entity.Dto.VideoDto;
using Entity.Entitys.VideoEntity;
using Microsoft.AspNetCore.Mvc;

namespace ServiceVideoSharing.Controllers.VideoControllers
{
    /// <summary>
    /// 微信相关接口控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class WechatController(WechatOAuthService wechatOAuthService, WechatAccessTokenService wechatAccessTokenService) : ControllerBase
    {
        private readonly WechatOAuthService _wechatOAuthService = wechatOAuthService;
        private readonly WechatAccessTokenService _wechatAccessTokenService = wechatAccessTokenService;

        /// <summary>
        /// 获取微信OAuth2.0授权URL
        /// </summary>
        /// <param name="requestDto">授权请求参数</param>
        /// <param name="inviterId">邀请人ID（可选）</param>
        /// <returns>授权URL</returns>
        [HttpGet("authorize")]
        public async Task<IActionResult> GetOAuthUrl([FromQuery] WechatOAuthRequestDto requestDto, [FromQuery] string? inviterId = null)
        {
            await LoggerHelper<WechatController>.InfoAsync($"收到获取微信OAuth授权URL请求，RedirectUri: {requestDto.RedirectUri}, Scope: {requestDto.Scope}, State: {requestDto.State}, InviterId: {inviterId}");

            try
            {
                if (!ModelState.IsValid)
                {
                    await LoggerHelper<WechatController>.WarnAsync($"获取授权URL参数验证失败: {ModelState}");
                    return BadRequest(new { success = false, message = "参数验证失败", errors = ModelState });
                }

                var authUrl = _wechatOAuthService.GenerateOAuthUrl(requestDto, inviterId);

                await LoggerHelper<WechatController>.InfoAsync($"成功生成微信OAuth授权URL: {authUrl}");
                return Ok(new
                {
                    success = true,
                    data = new { authUrl, inviterId },
                    message = "获取授权URL成功"
                });
            }
            catch (BusinessException ex)
            {
                await LoggerHelper<WechatController>.ErrorAsync($"获取微信OAuth授权URL业务异常: {ex.Message}", ex);
                return BadRequest(new { success = false, message = ex.Message });
            }
            catch (Exception ex)
            {
                await LoggerHelper<WechatController>.ErrorAsync($"获取微信OAuth授权URL系统异常: {ex.Message}", ex);
                return StatusCode(500, new { success = false, message = "服务器内部错误", detail = ex.Message });
            }
        }



        /// <summary>
        /// 检查微信配置
        /// </summary>
        /// <returns>配置状态</returns>
        [HttpGet("config-check")]
        public IActionResult CheckConfig()
        {
            try
            {
                LoggerHelper<WechatController>.Info("=== WechatController.CheckConfig 开始 ===");

                var appId = WxSetting.AppId;
                var appSecret = WxSetting.AppSecret;

                LoggerHelper<WechatController>.Info($"微信配置检查 - AppId: {appId}, AppSecret: {(string.IsNullOrEmpty(appSecret) ? "未设置" : $"已设置({appSecret.Length}位)")}");

                LoggerHelper<WechatController>.Info("=== WechatController.CheckConfig 完成 ===");

                return Ok(new
                {
                    success = true,
                    data = new
                    {
                        appId = appId,
                        appSecretConfigured = !string.IsNullOrEmpty(appSecret),
                        appSecretLength = appSecret?.Length ?? 0
                    },
                    message = "配置检查完成"
                });
            }
            catch (Exception ex)
            {
                LoggerHelper<WechatController>.Error($"检查微信配置异常: {ex.Message}", ex);
                return StatusCode(500, new { success = false, message = "配置检查失败", detail = ex.Message });
            }
        }

        /// <summary>
        /// 微信OAuth2.0授权回调接口
        /// </summary>
        /// <param name="callbackDto">回调参数</param>
        /// <returns>登录结果</returns>
        [HttpGet("callback")]
        public async Task<IActionResult> OAuthCallback([FromQuery] WechatOAuthCallbackDto callbackDto)
        {
            await LoggerHelper<WechatController>.InfoAsync("=== 微信OAuth回调接口开始 ===");
            await LoggerHelper<WechatController>.InfoAsync($"接收参数 - Code: {callbackDto.Code}, State: {callbackDto.State}");

            try
            {
                if (!ModelState.IsValid)
                {
                    await LoggerHelper<WechatController>.WarnAsync($"OAuth回调参数验证失败: {ModelState}");
                    return BadRequest(new { success = false, message = "参数验证失败", errors = ModelState });
                }

                await LoggerHelper<WechatController>.InfoAsync("开始调用WechatOAuthService处理回调");
                var loginResult = await _wechatOAuthService.HandleOAuthCallbackAsync(callbackDto);
                await LoggerHelper<WechatController>.InfoAsync("WechatOAuthService处理完成");

                await LoggerHelper<WechatController>.InfoAsync($"微信OAuth登录成功，用户ID: {loginResult.UserInfo.Id}, 昵称: {loginResult.UserInfo.Nickname}, 是否新用户: {loginResult.IsNewUser}, 审核状态: {loginResult.AuditStatus}, 邀请人: {loginResult.InviterId}");

                // 根据审核状态确定消息
                string message;
                if (loginResult.IsNewUser)
                {
                    message = loginResult.AuditStatus == 1 ? "新用户注册并登录成功" : "新用户注册成功，等待审核";
                }
                else
                {
                    message = loginResult.AuditStatus == 1 ? "用户登录成功" : "用户登录成功，但账户待审核";
                }

                var response = new
                {
                    success = true,
                    data = loginResult,
                    message = message
                };

                await LoggerHelper<WechatController>.InfoAsync("=== 微信OAuth回调接口成功完成 ===");
                await LoggerHelper<WechatController>.InfoAsync($"返回数据 - 用户ID: {loginResult.UserInfo.Id}, 管理Token长度: {loginResult.AccessToken?.Length ?? 0}位, 用户Token长度: {loginResult.UserToken?.Length ?? 0}位, 审核状态: {loginResult.AuditStatus}, 消息: {response.message}");

                return Ok(response);
            }
            catch (BusinessException ex)
            {
                await LoggerHelper<WechatController>.ErrorAsync($"微信OAuth登录业务异常: {ex.Message}", ex);
                return BadRequest(new { success = false, message = ex.Message });
            }
            catch (Exception ex)
            {
                await LoggerHelper<WechatController>.ErrorAsync($"微信OAuth登录系统异常: {ex.Message}", ex);
                return StatusCode(500, new { success = false, message = "登录失败", detail = ex.Message });
            }
        }

        /// <summary>
        /// 手动刷新微信基础access_token
        /// </summary>
        /// <returns>刷新结果</returns>
        [HttpPost("token/refresh")]
        public async Task<IActionResult> RefreshBasicAccessToken()
        {
            await LoggerHelper<WechatController>.InfoAsync("收到手动刷新微信基础access_token请求");

            try
            {
                var success = await _wechatOAuthService.RefreshBasicAccessTokenAsync();

                if (success)
                {
                    await LoggerHelper<WechatController>.InfoAsync("微信基础access_token刷新成功");
                    return Ok(new { success = true, message = "微信基础access_token刷新成功" });
                }
                else
                {
                    await LoggerHelper<WechatController>.WarnAsync("微信基础access_token刷新失败");
                    return BadRequest(new { success = false, message = "微信基础access_token刷新失败" });
                }
            }
            catch (BusinessException ex)
            {
                await LoggerHelper<WechatController>.ErrorAsync($"刷新微信基础access_token业务异常: {ex.Message}", ex);
                return BadRequest(new { success = false, message = ex.Message });
            }
            catch (Exception ex)
            {
                await LoggerHelper<WechatController>.ErrorAsync($"刷新微信基础access_token系统异常: {ex.Message}", ex);
                return StatusCode(500, new { success = false, message = "刷新失败", detail = ex.Message });
            }
        }

        /// <summary>
        /// 获取当前微信基础access_token状态
        /// </summary>
        /// <returns>Token状态</returns>
        [HttpGet("token/status")]
        public async Task<IActionResult> GetTokenStatus()
        {
            try
            {
                var appId = WxSetting.AppId;
                if (string.IsNullOrEmpty(appId))
                {
                    return BadRequest(new { success = false, message = "微信AppID未配置" });
                }

                var token = await _wechatAccessTokenService.GetValidAccessTokenAsync(appId, "basic");

                if (token != null)
                {
                    return Ok(new
                    {
                        success = true,
                        data = new
                        {
                            hasValidToken = true,
                            accessToken = token
                        },
                        message = "Token状态正常"
                    });
                }
                else
                {
                    return Ok(new
                    {
                        success = true,
                        data = new
                        {
                            hasValidToken = false,
                            expiresAt = (DateTime?)null,
                            remainingMinutes = 0
                        },
                        message = "无有效Token"
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, message = "获取Token状态失败", detail = ex.Message });
            }
        }

        /// <summary>
        /// 微信服务器验证接口（用于配置微信公众号）
        /// </summary>
        /// <param name="signature">微信加密签名</param>
        /// <param name="timestamp">时间戳</param>
        /// <param name="nonce">随机数</param>
        /// <param name="echostr">随机字符串</param>
        /// <returns>验证结果</returns>
        [HttpGet("verify")]
        public IActionResult VerifyWechatServer(string signature, string timestamp, string nonce, string echostr)
        {
            try
            {
                // 这里应该实现微信服务器验证逻辑
                // 暂时直接返回echostr用于开发测试
                // 生产环境需要验证signature

                if (string.IsNullOrEmpty(echostr))
                {
                    return BadRequest("Invalid verification request");
                }

                return Content(echostr, "text/plain");
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, message = "验证失败", detail = ex.Message });
            }
        }















        /// <summary>
        /// 生成JWT Token（私有方法）
        /// </summary>
        /// <param name="user">用户信息</param>
        /// <returns>JWT Token</returns>
        private static string GenerateJwtToken(User user)
        {
            var userInfo = new UserInfo
            {
                UserId = user.Id,
                UserName = user.Nickname ?? $"用户{user.Id}",
                IsAdmin = false,
                UserType = 4, // 普通用户类型
                Roles = []
            };

            return JWTHelper.CreateJwt(userInfo);
        }










    }
}
