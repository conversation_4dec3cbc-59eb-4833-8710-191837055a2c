using System.ComponentModel.DataAnnotations;
using Common.WX;
using Newtonsoft.Json;

namespace Entity.Dto.VideoDto
{
    /// <summary>
    /// 微信OAuth授权请求DTO
    /// </summary>
    public class WechatOAuthRequestDto
    {
        /// <summary>
        /// 授权后重定向的回调链接地址
        /// </summary>
        [Required(ErrorMessage = "重定向地址不能为空")]
        [Url(ErrorMessage = "重定向地址格式不正确")]
        public string RedirectUri { get; set; } = string.Empty;

        /// <summary>
        /// 重定向后会带上state参数，开发者可以填写a-zA-Z0-9的参数值，最多128字节
        /// </summary>
        [MaxLength(128, ErrorMessage = "状态参数长度不能超过128个字符")]
        public string? State { get; set; }

        /// <summary>
        /// 应用授权作用域，snsapi_base （不弹出授权页面，直接跳转，只能获取用户openid），snsapi_userinfo （弹出授权页面，可通过openid拿到昵称、性别、所在地。并且， 即使在未关注的情况下，只要用户授权，也能获取其信息 ）
        /// </summary>
        public string Scope { get; set; } = "snsapi_userinfo";
    }

    /// <summary>
    /// 微信OAuth回调DTO
    /// </summary>
    public class WechatOAuthCallbackDto
    {
        /// <summary>
        /// 授权码
        /// </summary>
        [Required(ErrorMessage = "授权码不能为空")]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// 状态参数
        /// </summary>
        public string? State { get; set; }
    }

    /// <summary>
    /// 微信OAuth登录响应DTO
    /// </summary>
    public class WechatOAuthLoginResponseDto
    {
        /// <summary>
        /// 管理系统JWT访问令牌（用于API身份验证，短期有效）
        /// </summary>
        public string AccessToken { get; set; } = string.Empty;

        /// <summary>
        /// 用户端业务Token（用于观看视频、答题、红包等业务功能，长期有效）
        /// </summary>
        public string UserToken { get; set; } = string.Empty;

        /// <summary>
        /// 用户端Token有效期（天数）
        /// </summary>
        public int UserTokenExpiryDays { get; set; } = 7;

        /// <summary>
        /// 用户信息
        /// </summary>
        public UserResponseDto UserInfo { get; set; } = new();

        /// <summary>
        /// 是否为新用户
        /// </summary>
        public bool IsNewUser { get; set; }

        /// <summary>
        /// 审核状态：0=待审核，1=已通过，2=已拒绝
        /// </summary>
        public int AuditStatus { get; set; }

        /// <summary>
        /// 邀请人ID
        /// </summary>
        public string? InviterId { get; set; }
    }

    /// <summary>
    /// 微信OAuth State参数DTO
    /// </summary>
    public class WechatOAuthStateDto
    {
        /// <summary>
        /// 时间戳
        /// </summary>
        public long Timestamp { get; set; }

        /// <summary>
        /// 随机字符串
        /// </summary>
        public string Random { get; set; } = string.Empty;

        /// <summary>
        /// 返回URL
        /// </summary>
        public string? ReturnUrl { get; set; }

        /// <summary>
        /// 邀请人ID（分享人的员工ID）
        /// </summary>
        [JsonProperty("InviterId")]
        public string? InviterId { get; set; }
    }


}
