[{"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController", "Method": "GetAnswerStatisticsCompat", "RelativePath": "api/AnswerRecord/statistics/{batchId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "batchId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.BatchStatisticsFromRecordDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "AnswerRecord_GetStatistics_Compat"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController", "Method": "SubmitAnswerRecordCompat", "RelativePath": "api/AnswerRecord/submit", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "answerDto", "Type": "Entity.Dto.VideoDto.AnswerSubmitDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "AnswerRecord_Submit_Compat"}, {"ContainingType": "ServiceVideoSharing.Controllers.SysControllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "loginRequest", "Type": "Entity.Dto.SysLoginRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.SysLoginResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"ContainingType": "ServiceVideoSharing.Controllers.SysControllers.AuthController", "Method": "Logout", "RelativePath": "api/Auth/logout", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "<PERSON><PERSON><PERSON><PERSON>"}, {"ContainingType": "ServiceVideoSharing.Controllers.SysControllers.AuthController", "Method": "GetUserInfo", "RelativePath": "api/Auth/userinfo", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.SysUserInfoDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "Auth_GetUserInfo"}, {"ContainingType": "ServiceVideoSharing.Controllers.BasisController.BasisController", "Method": "UploadFileAsync", "RelativePath": "api/Basis/UploadFileAsync", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "Basis_UploadFile"}, {"ContainingType": "ServiceVideoSharing.Controllers.BasisController.BasisController", "Method": "UploadFilesAsync", "RelativePath": "api/Basis/UploadFilesAsync", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "files", "Type": "System.Collections.Generic.List`1[[Microsoft.AspNetCore.Http.IFormFile, Microsoft.AspNetCore.Http.Features, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60]]", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "Basis_UploadFiles"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.BatchController", "Method": "Add", "RelativePath": "api/Batch", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createDto", "Type": "Entity.Dto.VideoDto.BatchCreateDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "Batch_Add"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.BatchController", "Method": "GetList", "RelativePath": "api/Batch", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "CreatedBy", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.Nullable`1[[System.Byte, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "StartTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "OrderField", "Type": "System.String", "IsRequired": false}, {"Name": "IsAsc", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.PagedResult`1[[Entity.Dto.VideoDto.BatchResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]], Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "Batch_GetList"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.BatchController", "Method": "Delete", "RelativePath": "api/Batch/{batchId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "batchId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "Batch_Delete"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.BatchController", "Method": "Get", "RelativePath": "api/Batch/{batchId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "batchId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.BatchResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "Batch_Get"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.BatchController", "Method": "GetBatchStatistics", "RelativePath": "api/Batch/{batchId}/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "batchId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.BatchStatisticsDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "Batch_GetStatistics"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.DashboardController", "Method": "GetDashboard", "RelativePath": "api/Dashboard", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.DashboardDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.DashboardController", "Method": "GetAnswerStatistics", "RelativePath": "api/Dashboard/answer", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.AnswerStatisticsDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.DashboardController", "Method": "GetTodayVsYesterday", "RelativePath": "api/Dashboard/compare/today-yesterday", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.DashboardController", "Method": "GetCourseStatistics", "RelativePath": "api/Dashboard/course", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.CourseStatisticsDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.DashboardController", "Method": "GetKeyMetrics", "RelativePath": "api/Dashboard/metrics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[BLL.VideoService.KeyMetricsSummaryDto, BLL, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.DashboardController", "Method": "GetOrderStatistics", "RelativePath": "api/Dashboard/order", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.OrderStatisticsDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.DashboardController", "Method": "GetRewardStatistics", "RelativePath": "api/Dashboard/reward", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.RewardStatisticsDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.DashboardController", "Method": "GetSummary", "RelativePath": "api/Dashboard/summary", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.DashboardSummaryDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.DashboardController", "Method": "GetTagStatistics", "RelativePath": "api/Dashboard/tags", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.List`1[[Entity.Dto.VideoDto.TagStatisticsDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.DashboardController", "Method": "GetThisMonthSnapshot", "RelativePath": "api/Dashboard/thisMonth", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.DashboardDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.DashboardController", "Method": "GetTodaySnapshot", "RelativePath": "api/Dashboard/today", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.DashboardDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.SysControllers.LogController", "Method": "GetAsync", "RelativePath": "api/Log/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Entitys.SysEntity.SysLog, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "Log_Get"}, {"ContainingType": "ServiceVideoSharing.Controllers.SysControllers.LogController", "Method": "ClearLogsAsync", "RelativePath": "api/Log/clear", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "clearDto", "Type": "Entity.Dto.LogClearDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.SysControllers.LogController", "Method": "ExportLogsAsync", "RelativePath": "api/Log/export", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.String", "IsRequired": false}, {"Name": "UserId", "Type": "System.String", "IsRequired": false}, {"Name": "Username", "Type": "System.String", "IsRequired": false}, {"Name": "Operation", "Type": "System.String", "IsRequired": false}, {"Name": "Method", "Type": "System.String", "IsRequired": false}, {"Name": "LogType", "Type": "System.String", "IsRequired": false}, {"Name": "LogLevel", "Type": "System.String", "IsRequired": false}, {"Name": "StartTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "OrderByCreateTime", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "exportRequest", "Type": "BLL.SysService.Exports.ExportRequestDto", "IsRequired": true}], "ReturnTypes": [], "EndpointName": "Log_Export"}, {"ContainingType": "ServiceVideoSharing.Controllers.SysControllers.LogController", "Method": "QueryAsync", "RelativePath": "api/Log/query", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.String", "IsRequired": false}, {"Name": "UserId", "Type": "System.String", "IsRequired": false}, {"Name": "Username", "Type": "System.String", "IsRequired": false}, {"Name": "Operation", "Type": "System.String", "IsRequired": false}, {"Name": "Method", "Type": "System.String", "IsRequired": false}, {"Name": "LogType", "Type": "System.String", "IsRequired": false}, {"Name": "LogLevel", "Type": "System.String", "IsRequired": false}, {"Name": "StartTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "OrderByCreateTime", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[DAL.Databases.EFHelper+PageEntity`1[[Entity.Entitys.SysEntity.SysLog, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]], DAL, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "Log_Query"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController", "Method": "GetUserRewardsCompat", "RelativePath": "api/Reward/user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.List`1[[Entity.Dto.VideoDto.UserBatchRecordResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "Reward_GetUserRewards_Compat"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.StatisticsController", "Method": "GetDailyTrend", "RelativePath": "api/Statistics/daily-trend", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userIds", "Type": "System.String", "IsRequired": false}, {"Name": "employeeId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "startDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.List`1[[Entity.Dto.VideoDto.DailyStatisticsTrendDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.StatisticsController", "Method": "GetDashboard", "RelativePath": "api/Statistics/dashboard", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.DashboardDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.StatisticsController", "Method": "GetKeyMetrics", "RelativePath": "api/Statistics/dashboard/key-metrics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[BLL.VideoService.KeyMetricsSummaryDto, BLL, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.StatisticsController", "Method": "GetTodaySnapshot", "RelativePath": "api/Statistics/dashboard/today", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.DashboardDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.StatisticsController", "Method": "GetEmployeeSummary", "RelativePath": "api/Statistics/employee-summary/{employeeId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "employeeId", "Type": "System.Int32", "IsRequired": true}, {"Name": "startDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.UserStatisticsSummaryDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.StatisticsController", "Method": "GetMyStatistics", "RelativePath": "api/Statistics/my-statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.List`1[[Entity.Dto.VideoDto.StatisticsResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.StatisticsController", "Method": "GetStatisticsOverview", "RelativePath": "api/Statistics/overview", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.StatisticsOverviewDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.StatisticsController", "Method": "GetStatisticsSummary", "RelativePath": "api/Statistics/summary", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "summaryDto", "Type": "Entity.Dto.VideoDto.StatisticsSummaryQueryDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.StatisticsSummaryDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.StatisticsController", "Method": "GetUserDailyStatistics", "RelativePath": "api/Statistics/user-daily", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "UserId", "Type": "System.String", "IsRequired": false}, {"Name": "EmployeeId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "StartDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UserIds", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "OrderField", "Type": "System.String", "IsRequired": false}, {"Name": "IsAsc", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.PagedResult`1[[Entity.Dto.VideoDto.UserDailyStatisticsDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]], Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.StatisticsController", "Method": "GetUserSummary", "RelativePath": "api/Statistics/user-summary/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}, {"Name": "startDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.UserStatisticsSummaryDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.StatisticsController", "Method": "GetUserStatistics", "RelativePath": "api/Statistics/user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}, {"Name": "startDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.List`1[[Entity.Dto.VideoDto.StatisticsResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.StatisticsController", "Method": "GetUsersSummary", "RelativePath": "api/Statistics/users-summary", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userIds", "Type": "System.String", "IsRequired": false}, {"Name": "startDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.List`1[[Entity.Dto.VideoDto.UserStatisticsSummaryDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.SystemConfigController", "Method": "AddSystemConfig", "RelativePath": "api/SystemConfig", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createDto", "Type": "Entity.Dto.VideoDto.SystemConfigCreateDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "SystemConfig_Add"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.SystemConfigController", "Method": "GetSystemConfigPagedList", "RelativePath": "api/SystemConfig", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "ConfigType", "Type": "System.String", "IsRequired": false}, {"Name": "GroupName", "Type": "System.String", "IsRequired": false}, {"Name": "IsEnabled", "Type": "System.Nullable`1[[System.Byte, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "OrderField", "Type": "System.String", "IsRequired": false}, {"Name": "IsAsc", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.PagedResult`1[[Entity.Dto.VideoDto.SystemConfigResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]], Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.SystemConfigController", "Method": "DeleteSystemConfig", "RelativePath": "api/SystemConfig/{configId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "configId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "SystemConfig_Delete"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.SystemConfigController", "Method": "GetSystemConfig", "RelativePath": "api/SystemConfig/{configId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "configId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.SystemConfigResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.SystemConfigController", "Method": "UpdateConfigStatus", "RelativePath": "api/SystemConfig/{configId}/status", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "configId", "Type": "System.Int32", "IsRequired": true}, {"Name": "isEnabled", "Type": "System.Boolean", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.SystemConfigController", "Method": "GetAllSystemConfigs", "RelativePath": "api/SystemConfig/all", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.List`1[[Entity.Dto.VideoDto.SystemConfigResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.SystemConfigController", "Method": "BatchUpdateConfigs", "RelativePath": "api/SystemConfig/batch-update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "configUpdates", "Type": "System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.SystemConfigController", "Method": "GetConfigsByGroup", "RelativePath": "api/SystemConfig/group/{groupName}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "groupName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.List`1[[Entity.Dto.VideoDto.SystemConfigResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.SystemConfigController", "Method": "GetConfigGroups", "RelativePath": "api/SystemConfig/groups", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.SystemConfigController", "Method": "GetSystemConfigByKey", "RelativePath": "api/SystemConfig/key/{configKey}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "config<PERSON><PERSON>", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.SystemConfigResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.SystemConfigController", "Method": "GetRewardConfigs", "RelativePath": "api/SystemConfig/reward", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.SystemConfigController", "Method": "GetSystemConfigs", "RelativePath": "api/SystemConfig/system", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.SystemConfigController", "Method": "GetConfigsByType", "RelativePath": "api/SystemConfig/type/{configType}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "configType", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.List`1[[Entity.Dto.VideoDto.SystemConfigResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.SystemConfigController", "Method": "UpdateSystemConfig", "RelativePath": "api/SystemConfig/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "updateDto", "Type": "Entity.Dto.VideoDto.SystemConfigUpdateDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "SystemConfig_Update"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.SystemConfigController", "Method": "GetConfigValue", "RelativePath": "api/SystemConfig/value/{configKey}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "config<PERSON><PERSON>", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.SystemConfigController", "Method": "SetConfigValue", "RelativePath": "api/SystemConfig/value/{configKey}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "config<PERSON><PERSON>", "Type": "System.String", "IsRequired": true}, {"Name": "config<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.SystemConfigController", "Method": "GetWechatConfigs", "RelativePath": "api/SystemConfig/wechat", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.SysControllers.SysUserController", "Method": "GetAsync", "RelativePath": "api/SysUser/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.SysUserDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "SysUser_Get"}, {"ContainingType": "ServiceVideoSharing.Controllers.SysControllers.SysUserController", "Method": "GetAdministratorsAsync", "RelativePath": "api/SysUser/administrators", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "pageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "userName", "Type": "System.String", "IsRequired": false}, {"Name": "realName", "Type": "System.String", "IsRequired": false}, {"Name": "status", "Type": "System.Nullable`1[[System.Byte, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.List`1[[Entity.Dto.SysUserWithStatisticsDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "SysUser_GetAdministrators"}, {"ContainingType": "ServiceVideoSharing.Controllers.SysControllers.SysUserController", "Method": "ChangePasswordAsync", "RelativePath": "api/SysUser/change-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Entity.Dto.SysChangePasswordDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "SysUser_ChangePassword"}, {"ContainingType": "ServiceVideoSharing.Controllers.SysControllers.SysUserController", "Method": "CreateAsync", "RelativePath": "api/SysUser/create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Entity.Dto.SysCreateUserDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "SysUser_Create"}, {"ContainingType": "ServiceVideoSharing.Controllers.SysControllers.SysUserController", "Method": "DeleteAsync", "RelativePath": "api/SysUser/delete/{id}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "SysUser_Delete"}, {"ContainingType": "ServiceVideoSharing.Controllers.SysControllers.SysUserController", "Method": "GetEmployeesAsync", "RelativePath": "api/SysUser/employees", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "pageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "userName", "Type": "System.String", "IsRequired": false}, {"Name": "realName", "Type": "System.String", "IsRequired": false}, {"Name": "status", "Type": "System.Nullable`1[[System.Byte, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.List`1[[Entity.Dto.SysUserWithStatisticsDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "SysUser_GetEmployees"}, {"ContainingType": "ServiceVideoSharing.Controllers.SysControllers.SysUserController", "Method": "ResetPasswordAsync", "RelativePath": "api/SysUser/reset-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Entity.Dto.SysResetPasswordDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "SysUser_ResetPassword"}, {"ContainingType": "ServiceVideoSharing.Controllers.SysControllers.SysUserController", "Method": "GetSubordinatesAsync", "RelativePath": "api/SysUser/subordinates", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "pageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "userName", "Type": "System.String", "IsRequired": false}, {"Name": "realName", "Type": "System.String", "IsRequired": false}, {"Name": "status", "Type": "System.Nullable`1[[System.Byte, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.List`1[[Entity.Dto.SysUserWithStatisticsDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "SysUser_GetSubordinates"}, {"ContainingType": "ServiceVideoSharing.Controllers.SysControllers.SysUserController", "Method": "GetSubordinatesByIdAsync", "RelativePath": "api/SysUser/subordinates/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "startTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "pageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "userName", "Type": "System.String", "IsRequired": false}, {"Name": "realName", "Type": "System.String", "IsRequired": false}, {"Name": "status", "Type": "System.Nullable`1[[System.Byte, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.List`1[[Entity.Dto.SysUserWithStatisticsDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "SysUser_GetSubordinatesById"}, {"ContainingType": "ServiceVideoSharing.Controllers.SysControllers.SysUserController", "Method": "UpdateAsync", "RelativePath": "api/SysUser/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Entity.Dto.SysUpdateUserDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "SysUser_Update"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.UserController", "Method": "GetUserPagedList", "RelativePath": "api/User", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Nickname", "Type": "System.String", "IsRequired": false}, {"Name": "EmployeeId", "Type": "System.String", "IsRequired": false}, {"Name": "StartTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "OrderField", "Type": "System.String", "IsRequired": false}, {"Name": "IsAsc", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.PagedResult`1[[Entity.Dto.VideoDto.UserResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]], Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "VideoUser_GetUserPagedList"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.UserController", "Method": "GetUser", "RelativePath": "api/User/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.UserResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "VideoUser_GetUser"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.UserController", "Method": "AccessPromotion", "RelativePath": "api/User/access-promotion", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "accessDto", "Type": "Entity.Dto.VideoDto.AccessPromotionDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.AccessPromotionResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "VideoUser_AccessPromotion"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.UserController", "Method": "GetProfile", "RelativePath": "api/User/profile", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.UserResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "VideoUser_GetProfile"}, {"ContainingType": "UserQuizController", "Method": "SubmitQuiz", "RelativePath": "api/user/quiz/{quizId}/submit", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "quizId", "Type": "System.String", "IsRequired": true}, {"Name": "answers", "Type": "System.String[]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "UserQuizController", "Method": "GetQuizList", "RelativePath": "api/user/quiz/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "UserRedPacketController", "Method": "ClaimRedPacket", "RelativePath": "api/user/redpacket/{redPacketId}/claim", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "redPacketId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "UserRedPacketController", "Method": "GetAvailableRedPackets", "RelativePath": "api/user/redpacket/available", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.UserController", "Method": "TransferUsers", "RelativePath": "api/User/transfer", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "transferDto", "Type": "Entity.Dto.VideoDto.UserTransferDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "VideoUser_TransferUsers"}, {"ContainingType": "ServiceVideoSharing.Controllers.UserControllers.UserVideoController", "Method": "RecordProgress", "RelativePath": "api/user/video/{videoId}/progress", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "videoId", "Type": "System.String", "IsRequired": true}, {"Name": "progress", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ServiceVideoSharing.Controllers.UserControllers.UserVideoController", "Method": "GetVideoList", "RelativePath": "api/user/video/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.UserAuditController", "Method": "GetAllPendingUsers", "RelativePath": "api/UserAudit/all-pending-users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.List`1[[Entity.Dto.VideoDto.UserResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "UserAudit_GetAllPendingUsers"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.UserAuditController", "Method": "AuditUser", "RelativePath": "api/UserAudit/audit-user/{userId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}, {"Name": "auditDto", "Type": "Entity.Dto.VideoDto.UserAuditDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "UserAudit_AuditUser"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.UserAuditController", "Method": "GetPendingUsers", "RelativePath": "api/UserAudit/pending-users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.List`1[[Entity.Dto.VideoDto.UserResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "UserAudit_GetPendingUsers"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController", "Method": "GetRecord", "RelativePath": "api/UserBatchRecord/{userId}/{batchId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}, {"Name": "batchId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.UserBatchRecordResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "UserBatchRecord_Get"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController", "Method": "GetAnswerDetail", "RelativePath": "api/UserBatchRecord/{userId}/{batchId}/answer-detail", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}, {"Name": "batchId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.AnswerDetailDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "UserBatchRecord_GetAnswerDetail"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController", "Method": "CheckAnswerEligibility", "RelativePath": "api/UserBatchRecord/{userId}/{batchId}/answer-eligibility", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}, {"Name": "batchId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.AnswerEligibilityDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "UserBatchRecord_CheckAnswerEligibility"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController", "Method": "GetAnswerStatus", "RelativePath": "api/UserBatchRecord/{userId}/{batchId}/answer-status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}, {"Name": "batchId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.AnswerStatusDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "UserBatchRecord_GetAnswerStatus"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController", "Method": "HasAnswered", "RelativePath": "api/UserBatchRecord/{userId}/{batchId}/has-answered", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}, {"Name": "batchId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "UserBatchRecord_HasAnswered"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController", "Method": "HasCompleted", "RelativePath": "api/UserBatchRecord/{userId}/{batchId}/has-completed", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}, {"Name": "batchId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "UserBatchRecord_HasCompleted"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController", "Method": "Has<PERSON><PERSON>ard", "RelativePath": "api/UserBatchRecord/{userId}/{batchId}/has-reward", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}, {"Name": "batchId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "UserBatchRecord_HasReward"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController", "Method": "HasWatched", "RelativePath": "api/UserBatchRecord/{userId}/{batchId}/has-watched", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}, {"Name": "batchId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "UserBatchRecord_HasWatched"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController", "Method": "CheckRewardEligibility", "RelativePath": "api/UserBatchRecord/{userId}/{batchId}/reward-eligibility", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}, {"Name": "batchId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.RewardEligibilityDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "UserBatchRecord_CheckRewardEligibility"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController", "Method": "GetRewardStatus", "RelativePath": "api/UserBatchRecord/{userId}/{batchId}/reward-status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}, {"Name": "batchId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.RewardStatusDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "UserBatchRecord_GetRewardStatus"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController", "Method": "StartWatching", "RelativePath": "api/UserBatchRecord/{userId}/{batchId}/start-watching", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}, {"Name": "batchId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "UserBatchRecord_StartWatching"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController", "Method": "GetWatchStatus", "RelativePath": "api/UserBatchRecord/{userId}/{batchId}/watch-status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}, {"Name": "batchId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.WatchStatusDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "UserBatchRecord_GetWatchStatus"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController", "Method": "<PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/UserBatchRecord/{userId}/grant-reward", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}, {"Name": "rewardDto", "Type": "Entity.Dto.VideoDto.RewardGrantDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "UserBatchRecord_GrantReward"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController", "Method": "SubmitAnswer", "RelativePath": "api/UserBatchRecord/{userId}/submit-answer", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}, {"Name": "answerDto", "Type": "Entity.Dto.VideoDto.AnswerSubmitDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "UserBatchRecord_SubmitAnswer"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController", "Method": "UpdateRewardStatus", "RelativePath": "api/UserBatchRecord/{userId}/update-reward-status", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}, {"Name": "statusDto", "Type": "Entity.Dto.VideoDto.RewardStatusUpdateDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "UserBatchRecord_UpdateRewardStatus"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController", "Method": "UpdateWatchProgress", "RelativePath": "api/UserBatchRecord/{userId}/watch-progress", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}, {"Name": "updateDto", "Type": "Entity.Dto.VideoDto.WatchProgressUpdateDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "UserBatchRecord_UpdateWatchProgress"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController", "Method": "BatchUpdateProgress", "RelativePath": "api/UserBatchRecord/batch-update-progress", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "progressUpdates", "Type": "System.Collections.Generic.List`1[[Entity.Dto.VideoDto.BatchWatchProgressUpdateDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "UserBatchRecord_BatchUpdateProgress"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController", "Method": "BatchGrantRewards", "RelativePath": "api/UserBatchRecord/batch/{batchId}/grant-rewards", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "batchId", "Type": "System.Int32", "IsRequired": true}, {"Name": "rewardAmount", "Type": "System.Decimal", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.BatchRewardResultDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "UserBatchRecord_BatchGrantRewards"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController", "Method": "GetBatchRecords", "RelativePath": "api/UserBatchRecord/batch/{batchId}/records", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "batchId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.List`1[[Entity.Dto.VideoDto.UserBatchRecordResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "UserBatchRecord_GetBatchRecords"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController", "Method": "GetBatchStatistics", "RelativePath": "api/UserBatchRecord/batch/{batchId}/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "batchId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.BatchStatisticsFromRecordDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "UserBatchRecord_GetBatchStatistics"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController", "Method": "CreateOrGetRecord", "RelativePath": "api/UserBatchRecord/create-or-get", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createDto", "Type": "Entity.Dto.VideoDto.UserBatchRecordCreateDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.UserBatchRecordResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "UserBatchRecord_CreateOrGet"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController", "Method": "GetUserRecords", "RelativePath": "api/UserBatchRecord/user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.List`1[[Entity.Dto.VideoDto.UserBatchRecordSummaryDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "UserBatchRecord_GetUserRecords"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.UserTransferController", "Method": "TransferUsers", "RelativePath": "api/UserTransfer", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "transferDto", "Type": "Entity.Dto.VideoDto.UserTransferDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "UserTransfer_Transfer"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.UserTransferController", "Method": "GetUserTransferPagedList", "RelativePath": "api/UserTransfer", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "UserId", "Type": "System.String", "IsRequired": false}, {"Name": "FromEmployeeId", "Type": "System.String", "IsRequired": false}, {"Name": "ToEmployeeId", "Type": "System.String", "IsRequired": false}, {"Name": "OperatorId", "Type": "System.String", "IsRequired": false}, {"Name": "OperatorName", "Type": "System.String", "IsRequired": false}, {"Name": "Reason", "Type": "System.String", "IsRequired": false}, {"Name": "StartTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "OrderField", "Type": "System.String", "IsRequired": false}, {"Name": "IsAsc", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.PagedResult`1[[Entity.Dto.VideoDto.UserTransferResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]], Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.VideoController", "Method": "Add", "RelativePath": "api/Video", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createDto", "Type": "Entity.Dto.VideoDto.VideoCreateDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "Video_Add"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.VideoController", "Method": "GetList", "RelativePath": "api/Video", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Title", "Type": "System.String", "IsRequired": false}, {"Name": "CreatedBy", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.Nullable`1[[System.Byte, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "StartTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "OrderField", "Type": "System.String", "IsRequired": false}, {"Name": "IsAsc", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.PagedResult`1[[Entity.Dto.VideoDto.VideoResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]], Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "Video_GetList"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.VideoController", "Method": "GetVideoStatistics", "RelativePath": "api/Video/{videoId}/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "videoId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.VideoStatisticsDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "Video_GetVideoStatistics"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.VideoController", "Method": "UpdateVideoStatus", "RelativePath": "api/Video/{videoId}/status", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "videoId", "Type": "System.Int32", "IsRequired": true}, {"Name": "statusDto", "Type": "Entity.Dto.VideoDto.VideoStatusUpdateDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "Video_UpdateVideoStatus"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.VideoController", "Method": "GetCompressionProgress", "RelativePath": "api/Video/compression-progress/{fileId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fileId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "Video_GetCompressionProgress"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.VideoController", "Method": "GetCreatorVideos", "RelativePath": "api/Video/creator/{created<PERSON><PERSON>}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "created<PERSON>y", "Type": "System.String", "IsRequired": true}, {"Name": "status", "Type": "System.Nullable`1[[System.Byte, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.List`1[[Entity.Dto.VideoDto.VideoResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "Video_GetCreatorVideos"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.VideoController", "Method": "Delete", "RelativePath": "api/Video/Delete/{videoId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "videoId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.VideoController", "Method": "FixCompressedVideos", "RelativePath": "api/Video/fix-compressed-videos", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "Video_FixCompressedVideos"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.VideoController", "Method": "Get", "RelativePath": "api/Video/Get/{videoId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "videoId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.VideoResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.VideoController", "Method": "GetMyVideos", "RelativePath": "api/Video/my-videos", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "status", "Type": "System.Nullable`1[[System.Byte, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.List`1[[Entity.Dto.VideoDto.VideoResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "Video_GetMyVideos"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.VideoController", "Method": "SearchVideos", "RelativePath": "api/Video/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "keyword", "Type": "System.String", "IsRequired": false}, {"Name": "pageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.PagedResult`1[[Entity.Dto.VideoDto.VideoResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]], Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "Video_SearchVideos"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.VideoController", "Method": "Update", "RelativePath": "api/Video/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "updateDto", "Type": "Entity.Dto.VideoDto.VideoUpdateDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "Video_Update"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.VideoController", "Method": "UploadVideo", "RelativePath": "api/Video/upload", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "VideoFile", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "EnableCompression", "Type": "System.Boolean", "IsRequired": false}, {"Name": "Title", "Type": "System.String", "IsRequired": false}, {"Name": "Description", "Type": "System.String", "IsRequired": false}, {"Name": "CompressionQuality", "Type": "System.Int32", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.VideoUploadResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "Video_Upload"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.VideoController", "Method": "UploadVideoComplete", "RelativePath": "api/Video/upload-complete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "VideoFile", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "CoverFile", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "CoverUrl", "Type": "System.String", "IsRequired": false}, {"Name": "Title", "Type": "System.String", "IsRequired": false}, {"Name": "Description", "Type": "System.String", "IsRequired": false}, {"Name": "RewardAmount", "Type": "System.Decimal", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "EnableCompression", "Type": "System.Boolean", "IsRequired": false}, {"Name": "CompressionQuality", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.VideoCompleteUploadResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "Video_UploadComplete"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.VideoController", "Method": "UploadVideoSimple", "RelativePath": "api/Video/upload-simple", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "videoFile", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "Video_UploadSimple"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController", "Method": "CreateViewRecordCompat", "RelativePath": "api/ViewRecord", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createDto", "Type": "Entity.Dto.VideoDto.UserBatchRecordCreateDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "ViewRecord_Create_Compat"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController", "Method": "GetBatchViewRecordsCompat", "RelativePath": "api/ViewRecord/batch/{batchId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "batchId", "Type": "System.Int32", "IsRequired": true}, {"Name": "pageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.PagedResult`1[[Entity.Dto.VideoDto.UserBatchRecordResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]], Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "ViewRecord_GetBatchRecords_Compat"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController", "Method": "GetViewStatisticsCompat", "RelativePath": "api/ViewRecord/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "batchId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.BatchStatisticsFromRecordDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "ViewRecord_GetStatistics_Compat"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController", "Method": "UpdateViewProgressCompat", "RelativePath": "api/ViewRecord/update-progress", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "updateDto", "Type": "Entity.Dto.VideoDto.WatchProgressUpdateDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "ViewRecord_UpdateProgress_Compat"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.WechatController", "Method": "GetOAuthUrl", "RelativePath": "api/Wechat/authorize", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "RedirectUri", "Type": "System.String", "IsRequired": false}, {"Name": "State", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "inviterId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.WechatController", "Method": "OAuthCallback", "RelativePath": "api/<PERSON><PERSON>t/callback", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Code", "Type": "System.String", "IsRequired": false}, {"Name": "State", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.WechatController", "Method": "CheckConfig", "RelativePath": "api/Wechat/config-check", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.WechatController", "Method": "InitTestEmployee", "RelativePath": "api/Wechat/init-test-employee", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.WechatController", "Method": "TestAuditUser", "RelativePath": "api/Wechat/test-audit", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": false}, {"Name": "auditStatus", "Type": "System.Int32", "IsRequired": false}, {"Name": "remark", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.WechatController", "Method": "Test<PERSON><PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/Wechat/test-callback", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Code", "Type": "System.String", "IsRequired": false}, {"Name": "State", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.WechatController", "Method": "TestVideoWatch", "RelativePath": "api/Wechat/test-video-watch", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userToken", "Type": "System.String", "IsRequired": false}, {"Name": "videoId", "Type": "System.String", "IsRequired": false}, {"Name": "watchDuration", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.WechatController", "Method": "RefreshBasicAccessToken", "RelativePath": "api/Wechat/token/refresh", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.WechatController", "Method": "GetTokenStatus", "RelativePath": "api/Wechat/token/status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.WechatController", "Method": "VerifyWechatServer", "RelativePath": "api/We<PERSON>t/verify", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "signature", "Type": "System.String", "IsRequired": false}, {"Name": "timestamp", "Type": "System.String", "IsRequired": false}, {"Name": "nonce", "Type": "System.String", "IsRequired": false}, {"Name": "echostr", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.WechatAccessTokenController", "Method": "CreateAccessToken", "RelativePath": "api/WechatAccessToken", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createDto", "Type": "Entity.Dto.VideoDto.WechatAccessTokenCreateDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "WechatAccessToken_Create"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.WechatAccessTokenController", "Method": "GetAccessTokenPagedList", "RelativePath": "api/WechatAccessToken", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "AppId", "Type": "System.String", "IsRequired": false}, {"Name": "TokenType", "Type": "System.String", "IsRequired": false}, {"Name": "Is<PERSON><PERSON><PERSON>", "Type": "System.Nullable`1[[System.Byte, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IsActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IsExpired", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ObtainStartTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ObtainEndTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ExpiresStartTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ExpiresEndTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "OrderField", "Type": "System.String", "IsRequired": false}, {"Name": "IsAsc", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.PagedResult`1[[Entity.Dto.VideoDto.WechatAccessTokenResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]], Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.WechatAccessTokenController", "Method": "GetAccessToken", "RelativePath": "api/WechatAccessToken/{tokenId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tokenId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.WechatAccessTokenResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.WechatAccessTokenController", "Method": "ToggleTokenStatus", "RelativePath": "api/WechatAccessToken/{tokenId}/status", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "tokenId", "Type": "System.Int32", "IsRequired": true}, {"Name": "isActive", "Type": "System.Boolean", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.WechatAccessTokenController", "Method": "GetAppTokens", "RelativePath": "api/WechatAccessToken/app/{appId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "appId", "Type": "System.String", "IsRequired": true}, {"Name": "isActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.List`1[[Entity.Dto.VideoDto.WechatAccessTokenResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.WechatAccessTokenController", "Method": "BatchRefreshAccessTokens", "RelativePath": "api/WechatAccessToken/batch-refresh", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "refreshRequests", "Type": "System.Collections.Generic.List`1[[Entity.Dto.VideoDto.TokenRefreshRequestDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.BatchRefreshResultDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.WechatAccessTokenController", "Method": "IsTokenExpiring", "RelativePath": "api/WechatAccessToken/check-expiring", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "appId", "Type": "System.String", "IsRequired": false}, {"Name": "tokenType", "Type": "System.String", "IsRequired": false}, {"Name": "bufferMinutes", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.WechatAccessTokenController", "Method": "CleanupExpiredTokens", "RelativePath": "api/WechatAccessToken/cleanup-expired", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.WechatAccessTokenController", "Method": "GetExpiringTokens", "RelativePath": "api/WechatAccessToken/expiring", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "bufferMinutes", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.List`1[[Entity.Dto.VideoDto.WechatAccessTokenResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.WechatAccessTokenController", "Method": "RefreshAccessToken", "RelativePath": "api/WechatAccessToken/refresh", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "refreshDto", "Type": "Entity.Dto.VideoDto.TokenRefreshRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.WechatAccessTokenController", "Method": "GetTokenStatistics", "RelativePath": "api/WechatAccessToken/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.WechatTokenStatisticsDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.WechatAccessTokenController", "Method": "UpdateAccessToken", "RelativePath": "api/WechatAccessToken/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "updateDto", "Type": "Entity.Dto.VideoDto.WechatAccessTokenUpdateDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "WechatAccessToken_Update"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.WechatAccessTokenController", "Method": "GetValidAccessToken", "RelativePath": "api/WechatAccessToken/valid", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "appId", "Type": "System.String", "IsRequired": false}, {"Name": "tokenType", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.WechatPaymentController", "Method": "GetRewardPagedList", "RelativePath": "api/WechatPayment", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "RewardId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UserId", "Type": "System.String", "IsRequired": false}, {"Name": "OpenId", "Type": "System.String", "IsRequired": false}, {"Name": "OutTradeNo", "Type": "System.String", "IsRequired": false}, {"Name": "TransactionId", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.Nullable`1[[System.Byte, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PayStartTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PayEndTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MinAmount", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MaxAmount", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "OrderNo", "Type": "System.String", "IsRequired": false}, {"Name": "PaymentType", "Type": "System.String", "IsRequired": false}, {"Name": "StartTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "OrderField", "Type": "System.String", "IsRequired": false}, {"Name": "IsAsc", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.PagedResult`1[[Entity.Dto.VideoDto.WechatPaymentResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]], Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.WechatPaymentController", "Method": "GetRewardRecord", "RelativePath": "api/WechatPayment/{paymentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "paymentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.WechatPaymentResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "WechatPayment_GetRewardRecord"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.WechatPaymentController", "Method": "RetryReward", "RelativePath": "api/WechatPayment/{paymentId}/retry", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "paymentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.WechatPaymentController", "Method": "QueryRewardStatus", "RelativePath": "api/WechatPayment/{paymentId}/status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "paymentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.WechatPaymentResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.WechatPaymentController", "Method": "UpdateRewardStatus", "RelativePath": "api/WechatPayment/{paymentId}/status", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "paymentId", "Type": "System.Int32", "IsRequired": true}, {"Name": "status", "Type": "System.Byte", "IsRequired": false}, {"Name": "transactionId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.WechatPaymentController", "Method": "BatchRetryRewards", "RelativePath": "api/WechatPayment/batch-retry", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "paymentIds", "Type": "System.Collections.Generic.List`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.WechatPaymentController", "Method": "WechatRewardCallback", "RelativePath": "api/WechatPayment/callback", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "callback<PERSON>to", "Type": "Entity.Dto.VideoDto.WechatPaymentCallbackDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.WechatPaymentController", "Method": "ExportRewards", "RelativePath": "api/WechatPayment/export", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "exportDto", "Type": "Entity.Dto.VideoDto.WechatPaymentQueryDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.List`1[[Entity.Dto.VideoDto.WechatPaymentResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.WechatPaymentController", "Method": "GetFailedRewards", "RelativePath": "api/WechatPayment/failed", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "RewardId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UserId", "Type": "System.String", "IsRequired": false}, {"Name": "OpenId", "Type": "System.String", "IsRequired": false}, {"Name": "OutTradeNo", "Type": "System.String", "IsRequired": false}, {"Name": "TransactionId", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.Nullable`1[[System.Byte, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PayStartTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PayEndTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MinAmount", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MaxAmount", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "OrderNo", "Type": "System.String", "IsRequired": false}, {"Name": "PaymentType", "Type": "System.String", "IsRequired": false}, {"Name": "StartTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "OrderField", "Type": "System.String", "IsRequired": false}, {"Name": "IsAsc", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.PagedResult`1[[Entity.Dto.VideoDto.WechatPaymentResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]], Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.WechatPaymentController", "Method": "GetMyRewards", "RelativePath": "api/WechatPayment/my-rewards", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "status", "Type": "System.Nullable`1[[System.Byte, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.List`1[[Entity.Dto.VideoDto.WechatPaymentResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.WechatPaymentController", "Method": "GetPendingRewards", "RelativePath": "api/WechatPayment/pending", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "RewardId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UserId", "Type": "System.String", "IsRequired": false}, {"Name": "OpenId", "Type": "System.String", "IsRequired": false}, {"Name": "OutTradeNo", "Type": "System.String", "IsRequired": false}, {"Name": "TransactionId", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.Nullable`1[[System.Byte, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PayStartTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PayEndTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MinAmount", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MaxAmount", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "OrderNo", "Type": "System.String", "IsRequired": false}, {"Name": "PaymentType", "Type": "System.String", "IsRequired": false}, {"Name": "StartTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "OrderField", "Type": "System.String", "IsRequired": false}, {"Name": "IsAsc", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.PagedResult`1[[Entity.Dto.VideoDto.WechatPaymentResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]], Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.WechatPaymentController", "Method": "SendReward", "RelativePath": "api/WechatPayment/send-reward", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createDto", "Type": "Entity.Dto.VideoDto.WechatPaymentCreateDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "WechatPayment_SendReward"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.WechatPaymentController", "Method": "GetRewardStatistics", "RelativePath": "api/WechatPayment/statistics", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "statisticsDto", "Type": "Entity.Dto.VideoDto.WechatPaymentQueryDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.WechatPaymentStatisticsDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.WechatPaymentController", "Method": "GetRewardByTradeNo", "RelativePath": "api/WechatPayment/trade/{outTradeNo}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "outTradeNo", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.VideoDto.WechatPaymentResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "WechatPayment_GetRewardByTradeNo"}, {"ContainingType": "ServiceVideoSharing.Controllers.VideoControllers.WechatPaymentController", "Method": "GetUserRewards", "RelativePath": "api/WechatPayment/user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}, {"Name": "status", "Type": "System.Nullable`1[[System.Byte, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.List`1[[Entity.Dto.VideoDto.WechatPaymentResponseDto, Entity, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}]