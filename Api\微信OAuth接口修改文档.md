# 微信OAuth接口修改文档

## 修改概述

为了加强安全性，微信OAuth授权接口进行了以下重要修改：
1. **邀请人ID改为必须参数**：所有用户绑定都必须有员工邀请
2. **增加邀请人身份验证**：只有员工和超级管理员才能邀请用户
3. **统一错误响应格式**：验证失败时返回统一的错误格式

## 接口变更详情

### 获取微信OAuth授权URL接口

**接口地址：** `POST /api/Wechat/oauth-url`

#### 修改前的调用方式

```javascript
// 旧的调用方式
const response = await fetch('/api/Wechat/oauth-url?inviterId=emp001', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        redirectUri: 'https://example.com/callback',
        state: 'some-state',
        scope: 'snsapi_userinfo'
    })
});
```

#### 修改后的调用方式

```javascript
// 新的调用方式 - InviterId 必须在请求体中
const response = await fetch('/api/Wechat/oauth-url', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        redirectUri: 'https://example.com/callback',
        state: 'some-state',
        scope: 'snsapi_userinfo',
        inviterId: 'emp001'  // 必须参数，不能为空
    })
});
```

#### 请求参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| redirectUri | string | 是 | 授权后重定向的回调链接地址 |
| state | string | 否 | 状态参数，最多128字符 |
| scope | string | 否 | 授权作用域，默认为 snsapi_userinfo |
| **inviterId** | **string** | **是** | **邀请人ID（员工ID），必须参数** |

#### 响应格式变更

##### 成功响应（无变化）
```json
{
    "success": true,
    "data": {
        "authUrl": "https://open.weixin.qq.com/connect/oauth2/authorize?...",
        "inviterId": "emp001"
    },
    "message": "获取授权URL成功"
}
```

##### 错误响应（重要变更）
```json
// 新的错误响应格式 - 注意：HTTP状态码为200，但code为500
{
    "success": false,
    "code": 500,
    "message": "邀请人异常"
}
```

**重要提醒：** 错误响应的HTTP状态码仍为200，需要通过响应体中的 `success` 和 `code` 字段判断是否成功。

#### 可能的错误情况

| 错误消息 | 说明 |
|----------|------|
| "邀请人ID不能为空" | 请求体中未提供 inviterId 参数 |
| "邀请人异常" | 邀请人不存在、不是员工身份或状态异常 |
| "参数验证失败" | 其他必填参数缺失或格式错误 |

## 前端代码修改建议

### 1. 参数传递修改

```javascript
// 修改前
function getWechatAuthUrl(redirectUri, state, inviterId) {
    return fetch(`/api/Wechat/oauth-url?inviterId=${inviterId}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ redirectUri, state })
    });
}

// 修改后
function getWechatAuthUrl(redirectUri, state, inviterId) {
    return fetch('/api/Wechat/oauth-url', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
            redirectUri, 
            state, 
            inviterId  // 移到请求体中，且为必填
        })
    });
}
```

### 2. 错误处理修改

```javascript
// 修改前
async function handleWechatAuth(redirectUri, state, inviterId) {
    try {
        const response = await getWechatAuthUrl(redirectUri, state, inviterId);
        
        if (!response.ok) {
            throw new Error('请求失败');
        }
        
        const data = await response.json();
        if (data.success) {
            window.location.href = data.data.authUrl;
        } else {
            alert(data.message);
        }
    } catch (error) {
        alert('获取授权链接失败');
    }
}

// 修改后
async function handleWechatAuth(redirectUri, state, inviterId) {
    try {
        const response = await getWechatAuthUrl(redirectUri, state, inviterId);
        const data = await response.json();
        
        // 重要：即使HTTP状态码为200，也要检查success和code
        if (data.success) {
            window.location.href = data.data.authUrl;
        } else {
            // 根据错误码和消息进行不同处理
            if (data.code === 500) {
                if (data.message === '邀请人异常') {
                    alert('邀请人身份验证失败，请联系管理员');
                } else {
                    alert(data.message);
                }
            } else {
                alert(data.message || '获取授权链接失败');
            }
        }
    } catch (error) {
        alert('网络错误，请稍后重试');
    }
}
```

### 3. 表单验证修改

```javascript
// 在调用接口前，确保邀请人ID不为空
function validateParams(redirectUri, inviterId) {
    if (!redirectUri) {
        alert('回调地址不能为空');
        return false;
    }
    
    if (!inviterId) {  // 新增验证
        alert('邀请人ID不能为空');
        return false;
    }
    
    return true;
}
```

## 测试建议

### 1. 正常流程测试
- 使用有效的员工ID作为邀请人
- 验证能正常获取授权URL

### 2. 异常情况测试
- 不传 inviterId 参数
- 传入不存在的员工ID
- 传入非员工身份的用户ID
- 传入已禁用的员工ID

### 3. 响应处理测试
- 验证错误响应的HTTP状态码为200
- 验证通过 `success` 和 `code` 字段正确判断结果

## 注意事项

1. **必须传递邀请人ID**：所有调用都必须提供有效的员工邀请人ID
2. **错误响应格式变更**：HTTP状态码为200，通过响应体判断成功失败
3. **邀请人身份限制**：只有员工(UserType=3)和超级管理员(UserType=1)可以邀请用户
4. **向后兼容性**：此修改不向后兼容，前端必须同步更新

## 联系方式

如有疑问，请联系后端开发团队。
